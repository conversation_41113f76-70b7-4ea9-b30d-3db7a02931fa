'use client'

import React, { useMemo } from 'react'
import { ModificationInfo } from '@/lib/store'
import { cn } from '@/lib/utils'
import { CheckCircle2, XCircle, FileText, Plus, Minus, Edit3 } from 'lucide-react'

interface DiffViewProps {
  originalContent: string
  modifications: ModificationInfo[]
  onAccept: (modId: string) => void
  onReject: (modId: string) => void
  onAcceptAll: () => void
}

const DiffView: React.FC<DiffViewProps> = ({
  originalContent,
  modifications,
  onAccept,
  onReject,
  onAcceptAll
}) => {
  // 将原始内容按行分割
  const originalLines = useMemo(() => originalContent.split('\n'), [originalContent])

  // 生成差异视图
  const diffBlocks = useMemo(() => {
    const blocks: Array<{
      type: 'unchanged' | 'modification'
      startLine: number
      endLine: number
      content: string[]
      modification?: ModificationInfo
    }> = []

    let lastLine = 0

    // 按行号排序修改
    const sortedMods = [...modifications].sort((a, b) => 
      (a.lineStart || 0) - (b.lineStart || 0)
    )

    sortedMods.forEach(mod => {
      const startLine = (mod.lineStart || 1) - 1
      const endLine = (mod.lineEnd || startLine + 1) - 1

      // 添加未修改的部分
      if (lastLine < startLine) {
        blocks.push({
          type: 'unchanged',
          startLine: lastLine,
          endLine: startLine - 1,
          content: originalLines.slice(lastLine, startLine)
        })
      }

      // 添加修改部分
      blocks.push({
        type: 'modification',
        startLine,
        endLine,
        content: originalLines.slice(startLine, endLine + 1),
        modification: mod
      })

      lastLine = endLine + 1
    })

    // 添加最后的未修改部分
    if (lastLine < originalLines.length) {
      blocks.push({
        type: 'unchanged',
        startLine: lastLine,
        endLine: originalLines.length - 1,
        content: originalLines.slice(lastLine)
      })
    }

    return blocks
  }, [originalLines, modifications])

  // 计算待处理的修改数量
  const pendingCount = modifications.filter(m => !m.accepted && !m.rejected).length

  // 获取修改类型的图标和颜色
  const getModTypeStyle = (type: ModificationInfo['type']) => {
    switch (type) {
      case 'add':
        return {
          icon: <Plus size={16} />,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        }
      case 'delete':
        return {
          icon: <Minus size={16} />,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        }
      case 'modify':
        return {
          icon: <Edit3 size={16} />,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        }
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* 标题栏 */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="w-5 h-5 text-gray-600" />
            <h2 className="text-lg font-semibold">修改对比视图</h2>
            <span className="text-sm text-gray-500">
              ({pendingCount} 个待处理修改)
            </span>
          </div>
          {pendingCount > 0 && (
            <button
              onClick={onAcceptAll}
              className="px-4 py-1.5 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm"
            >
              接受全部修改
            </button>
          )}
        </div>
      </div>

      {/* 差异内容 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4 font-mono text-sm">
          {diffBlocks.map((block, idx) => {
            if (block.type === 'unchanged') {
              // 未修改的内容
              return (
                <div key={idx} className="space-y-1">
                  {block.content.map((line, lineIdx) => (
                    <div key={lineIdx} className="flex">
                      <span className="w-12 text-right pr-2 text-gray-400 select-none">
                        {block.startLine + lineIdx + 1}
                      </span>
                      <span className="flex-1 text-gray-600 pl-2">
                        {line || '\u00A0'}
                      </span>
                    </div>
                  ))}
                </div>
              )
            } else {
              // 修改的内容
              const mod = block.modification!
              const style = getModTypeStyle(mod.type)
              const isProcessed = mod.accepted || mod.rejected

              return (
                <div
                  key={idx}
                  className={cn(
                    "rounded-lg border-2 overflow-hidden transition-all duration-200",
                    isProcessed ? 'opacity-60' : '',
                    style.borderColor
                  )}
                >
                  {/* 修改头部 */}
                  <div className={cn("p-3", style.bgColor)}>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-2">
                        <div className={cn("mt-0.5", style.color)}>
                          {style.icon}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {mod.section}
                          </div>
                          {mod.reason && (
                            <div className="text-xs text-gray-600 mt-1">
                              {mod.reason}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {mod.accepted && (
                          <span className="text-xs text-green-600 flex items-center">
                            <CheckCircle2 size={14} className="mr-1" />
                            已接受
                          </span>
                        )}
                        {mod.rejected && (
                          <span className="text-xs text-red-600 flex items-center">
                            <XCircle size={14} className="mr-1" />
                            已拒绝
                          </span>
                        )}
                        {!isProcessed && (
                          <>
                            <button
                              onClick={() => onAccept(mod.id)}
                              className="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600 transition-colors"
                            >
                              接受
                            </button>
                            <button
                              onClick={() => onReject(mod.id)}
                              className="px-2 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors"
                            >
                              拒绝
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 修改内容 */}
                  <div className="p-3 bg-white">
                    {mod.type === 'delete' ? (
                      // 删除内容 - 显示原始内容带删除线
                      <div className="space-y-1">
                        {block.content.map((line, lineIdx) => (
                          <div key={lineIdx} className="flex">
                            <span className="w-12 text-right pr-2 text-red-400 select-none">
                              -{block.startLine + lineIdx + 1}
                            </span>
                            <span className="flex-1 text-red-600 line-through pl-2 bg-red-50">
                              {line || '\u00A0'}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : mod.type === 'add' ? (
                      // 添加内容 - 显示新内容
                      <div className="space-y-1">
                        {mod.content.split('\n').map((line, lineIdx) => (
                          <div key={lineIdx} className="flex">
                            <span className="w-12 text-right pr-2 text-green-400 select-none">
                              +{block.startLine + lineIdx + 1}
                            </span>
                            <span className="flex-1 text-green-600 pl-2 bg-green-50">
                              {line || '\u00A0'}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      // 修改内容 - 显示对比
                      <>
                        <div className="space-y-1 mb-2">
                          {block.content.map((line, lineIdx) => (
                            <div key={lineIdx} className="flex">
                              <span className="w-12 text-right pr-2 text-red-400 select-none">
                                -{block.startLine + lineIdx + 1}
                              </span>
                              <span className="flex-1 text-red-600 line-through pl-2 bg-red-50">
                                {line || '\u00A0'}
                              </span>
                            </div>
                          ))}
                        </div>
                        <div className="space-y-1">
                          {mod.content.split('\n').map((line, lineIdx) => (
                            <div key={lineIdx} className="flex">
                              <span className="w-12 text-right pr-2 text-green-400 select-none">
                                +{block.startLine + lineIdx + 1}
                              </span>
                              <span className="flex-1 text-green-600 pl-2 bg-green-50">
                                {line || '\u00A0'}
                              </span>
                            </div>
                          ))}
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )
            }
          })}
        </div>
      </div>
    </div>
  )
}

export default DiffView