'use client'

import React, { useState } from 'react'
import { Check, X, CheckCircle2, <PERSON>Circle, Plus, Minus } from 'lucide-react'

interface DiffModification {
  type: 'add' | 'modify' | 'delete'
  section: string
  content: string
  reason: string
  id?: string
}

interface DiffViewerProps {
  modifications: DiffModification[]
  onAccept: (modificationId: string) => void
  onReject: (modificationId: string) => void
  onAcceptAll: () => void
  onRejectAll: () => void
  className?: string
}

const DiffViewer: React.FC<DiffViewerProps> = ({
  modifications,
  onAccept,
  onReject,
  onAcceptAll,
  onRejectAll,
  className = ''
}) => {
  const [acceptedModifications, setAcceptedModifications] = useState<Set<string>>(new Set())
  const [rejectedModifications, setRejectedModifications] = useState<Set<string>>(new Set())

  const handleAccept = (modificationId: string) => {
    setAcceptedModifications(prev => new Set([...prev, modificationId]))
    setRejectedModifications(prev => {
      const newSet = new Set(prev)
      newSet.delete(modificationId)
      return newSet
    })
    onAccept(modificationId)
  }

  const handleReject = (modificationId: string) => {
    setRejectedModifications(prev => new Set([...prev, modificationId]))
    setAcceptedModifications(prev => {
      const newSet = new Set(prev)
      newSet.delete(modificationId)
      return newSet
    })
    onReject(modificationId)
  }

  const handleAcceptAll = () => {
    const allIds = modifications.map((_, index) => index.toString())
    setAcceptedModifications(new Set(allIds))
    setRejectedModifications(new Set())
    onAcceptAll()
  }

  const handleRejectAll = () => {
    const allIds = modifications.map((_, index) => index.toString())
    setRejectedModifications(new Set(allIds))
    setAcceptedModifications(new Set())
    onRejectAll()
  }

  const getModificationIcon = (type: string) => {
    switch (type) {
      case 'add':
        return <Plus className="w-4 h-4 text-green-600" />
      case 'delete':
        return <Minus className="w-4 h-4 text-red-600" />
      case 'modify':
        return <div className="w-4 h-4 rounded-full bg-blue-600" />
      default:
        return null
    }
  }

  const getModificationBg = (type: string) => {
    switch (type) {
      case 'add':
        return 'bg-green-50 border-green-200'
      case 'delete':
        return 'bg-red-50 border-red-200'
      case 'modify':
        return 'bg-blue-50 border-blue-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  const getModificationTextColor = (type: string) => {
    switch (type) {
      case 'add':
        return 'text-green-800'
      case 'delete':
        return 'text-red-800'
      case 'modify':
        return 'text-blue-800'
      default:
        return 'text-gray-800'
    }
  }

  if (!modifications || modifications.length === 0) {
    return null
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      {/* 头部操作栏 */}
      <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <h3 className="text-sm font-medium text-gray-900">
              结构化笔记修改建议 ({modifications.length})
            </h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleAcceptAll}
              className="px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors duration-200 flex items-center space-x-1"
            >
              <CheckCircle2 className="w-3 h-3" />
              <span>接受全部</span>
            </button>
            <button
              onClick={handleRejectAll}
              className="px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors duration-200 flex items-center space-x-1"
            >
              <XCircle className="w-3 h-3" />
              <span>拒绝全部</span>
            </button>
          </div>
        </div>
      </div>

      {/* 修改列表 */}
      <div className="divide-y divide-gray-200">
        {modifications.map((modification, index) => {
          const modificationId = modification.id || index.toString()
          const isAccepted = acceptedModifications.has(modificationId)
          const isRejected = rejectedModifications.has(modificationId)
          
          return (
            <div
              key={modificationId}
              className={`p-4 transition-all duration-200 ${
                isAccepted ? 'bg-green-50 opacity-75' : 
                isRejected ? 'bg-red-50 opacity-75' : 
                'hover:bg-gray-50'
              }`}
            >
              <div className="flex items-start space-x-3">
                {/* 修改类型图标 */}
                <div className="flex-shrink-0 mt-1">
                  {getModificationIcon(modification.type)}
                </div>

                {/* 修改内容 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      {modification.type === 'add' ? '新增' : 
                       modification.type === 'delete' ? '删除' : '修改'}
                    </span>
                    <span className="text-sm font-medium text-gray-900">
                      {modification.section}
                    </span>
                  </div>
                  
                  <div className={`p-3 rounded-md border ${getModificationBg(modification.type)}`}>
                    <pre className={`text-sm whitespace-pre-wrap font-mono ${getModificationTextColor(modification.type)}`}>
                      {modification.content}
                    </pre>
                  </div>
                  
                  {modification.reason && (
                    <p className="mt-2 text-xs text-gray-600">
                      <span className="font-medium">原因：</span>
                      {modification.reason}
                    </p>
                  )}
                </div>

                {/* 操作按钮 */}
                <div className="flex-shrink-0 flex items-center space-x-2">
                  {isAccepted ? (
                    <div className="flex items-center space-x-1 text-green-600">
                      <CheckCircle2 className="w-4 h-4" />
                      <span className="text-xs font-medium">已接受</span>
                    </div>
                  ) : isRejected ? (
                    <div className="flex items-center space-x-1 text-red-600">
                      <XCircle className="w-4 h-4" />
                      <span className="text-xs font-medium">已拒绝</span>
                    </div>
                  ) : (
                    <>
                      <button
                        onClick={() => handleAccept(modificationId)}
                        className="p-1.5 text-green-600 hover:bg-green-100 rounded-md transition-colors duration-200"
                        title="接受此修改"
                      >
                        <Check className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleReject(modificationId)}
                        className="p-1.5 text-red-600 hover:bg-red-100 rounded-md transition-colors duration-200"
                        title="拒绝此修改"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default DiffViewer
