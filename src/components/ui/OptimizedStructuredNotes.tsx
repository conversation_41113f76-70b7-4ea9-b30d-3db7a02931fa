'use client'

import React, { memo, useMemo, useCallback, useRef, useEffect, useState } from 'react'
import { <PERSON>rkles, ChevronUp, ChevronDown, Save, Check, X, ArrowDown, Eye, EyeOff } from 'lucide-react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import { useSmartScrollFollow } from '@/hooks/useSmartScrollFollow'
import { useAppStore, useActiveTab, useActiveChatMessages } from '@/lib/store'

interface OptimizedStructuredNotesProps {
  content: string
  streamingContent?: string
  isAnalyzing?: boolean
  isExpanded: boolean
  height: number
  onToggleExpanded: () => void
  onManualExpand: () => void
  onManualCollapse: () => void
  className?: string
  fullscreen?: boolean // 新增：全屏模式，无边框无卡片样式
}

// 使用memo优化渲染性能
const OptimizedStructuredNotes = memo<OptimizedStructuredNotesProps>(({
  content,
  streamingContent,
  isAnalyzing,
  isExpanded,
  height,
  onToggleExpanded,
  onManualExpand,
  onManualCollapse,
  className = '',
  fullscreen = false
}) => {
  const dragHandleRef = useRef<HTMLDivElement>(null)
  const { saveNote, mode } = useAppStore()
  const activeTab = useActiveTab()
  const chatMessages = useActiveChatMessages()

  // 保存状态
  const [isSaving, setIsSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')
  
  // 智能滚动跟随 - 增强版本
  const {
    containerRef: scrollContainerRef,
    scrollState,
    forceScrollToBottom,
    scrollToBottom,
    showScrollButton,
    isUserScrolling,
    shouldAutoFollow,
    setManualOverride
  } = useSmartScrollFollow({
    enabled: isExpanded && (!!streamingContent || isAnalyzing),
    threshold: 50,
    smoothScroll: true,
    debounceMs: 100,
    manualOverrideDuration: 2000,
    minScrollDelta: 10
  })

  // 使用useMemo优化内容计算
  const displayContent = useMemo(() => {
    return streamingContent || content || ''
  }, [streamingContent, content])

  // 使用useMemo优化状态计算
  const noteStatus = useMemo(() => {
    if (isAnalyzing) return 'analyzing'
    if (streamingContent) return 'streaming'
    if (content) return 'completed'
    return 'empty'
  }, [isAnalyzing, streamingContent, content])

  // 优化的切换处理函数
  const handleToggle = useCallback(() => {
    if (isExpanded) {
      onManualCollapse()
    } else {
      onManualExpand()
    }
  }, [isExpanded, onManualExpand, onManualCollapse])

  // 监听内容变化，触发滚动跟随 - 优化版本
  useEffect(() => {
    if (streamingContent && isExpanded && shouldAutoFollow && !isUserScrolling) {
      // 使用防抖延迟滚动，避免过于频繁的滚动
      const timeoutId = setTimeout(() => {
        scrollToBottom()
      }, 50) // 减少延迟，提高响应性

      return () => clearTimeout(timeoutId)
    }
  }, [streamingContent, isExpanded, shouldAutoFollow, isUserScrolling, scrollToBottom])

  // 手动滚动处理
  const handleManualScroll = useCallback(() => {
    setManualOverride()
  }, [setManualOverride])

  // 滚动到底部按钮点击处理
  const handleScrollToBottomClick = useCallback(() => {
    forceScrollToBottom()
    // 埋点统计
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'scroll_to_bottom_click', {
        event_category: 'user_interaction',
        event_label: 'structured_notes'
      })
    }
  }, [forceScrollToBottom])

  // 保存状态重置
  useEffect(() => {
    if (saveStatus !== 'idle') {
      const timer = setTimeout(() => {
        setSaveStatus('idle')
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [saveStatus])

  // 保存笔记功能
  const handleSaveNote = useCallback(async () => {
    if (!activeTab || isSaving) return

    setIsSaving(true)
    setSaveStatus('idle')

    try {
      // 首先整合对话内容
      const integrateResponse = await fetch('/api/notes/integrate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          originalContent: activeTab.originalContent,
          structuredNotes: displayContent,
          chatMessages: chatMessages
        })
      })

      if (!integrateResponse.ok) {
        throw new Error('整合对话内容失败')
      }

      const { integratedNotes } = await integrateResponse.json()

      // 保存整合后的笔记
      const savedNote = await saveNote({
        title: activeTab.title,
        originalContent: activeTab.originalContent,
        structuredNotes: displayContent,
        integratedNotes,
        sourceType: activeTab.sourceType,
        sourceData: activeTab.sourceData
      })

      if (savedNote) {
        setSaveStatus('success')
      } else {
        setSaveStatus('error')
      }
    } catch (error) {
      console.error('保存笔记失败:', error)
      setSaveStatus('error')
    } finally {
      setIsSaving(false)
    }
  }, [activeTab, chatMessages, displayContent, saveNote, isSaving])

  // 判断是否可以保存
  const canSave = useMemo(() => {
    return activeTab && displayContent && !isAnalyzing && !streamingContent
  }, [activeTab, displayContent, isAnalyzing, streamingContent])

  // 渲染状态指示器 - 根据用户要求移除状态标识
  const renderStatusIndicator = useCallback(() => {
    // 移除所有状态指示器
    return null
  }, [])

  // 渲染内容区域
  const renderContent = useCallback(() => {
    if (noteStatus === 'empty') {
      return (
        <div className="py-8 text-center">
          <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-3">
            <Sparkles className="w-6 h-6 text-gray-400" />
          </div>
          <p className="text-gray-500 text-sm">暂无结构化笔记</p>
        </div>
      )
    }

    if (noteStatus === 'analyzing') {
      return (
        <div className="py-8">
          <ModernLoader variant="dots" size="md" text="正在生成结构化笔记..." className="text-center" />
        </div>
      )
    }

    // 全屏模式下的内容渲染 - 无装饰背景
    if (fullscreen) {
      return (
        <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
          {displayContent}
        </SafeMarkdown>
      )
    }

    // 卡片模式下的内容渲染 - 带装饰背景
    return (
      <div className="bg-gradient-to-br from-slate-50/50 to-blue-50/30 rounded-xl p-4">
        <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
          {displayContent}
        </SafeMarkdown>

        {/* 流式生成时的滚动指示器 - 优化版本 */}
        {showScrollButton && (
          <div className="absolute bottom-4 right-4 z-10">
            <button
              onClick={handleScrollToBottomClick}
              className={`group relative p-2.5 rounded-full shadow-lg transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                noteStatus === 'streaming'
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 focus:ring-blue-500 animate-pulse'
                  : 'bg-white text-gray-600 border border-gray-200 hover:bg-gray-50 focus:ring-gray-500'
              }`}
              title={noteStatus === 'streaming' ? '正在生成，点击跟随' : '滚动到底部'}
            >
              <ArrowDown className="w-4 h-4" />
              {noteStatus === 'streaming' && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-ping" />
                </div>
              )}
            </button>
          </div>
        )}
      </div>
    )
  }, [noteStatus, displayContent, scrollState.isAtBottom, forceScrollToBottom, fullscreen])

  // 全屏模式渲染
  if (fullscreen) {
    return (
      <div className={`h-full w-full ${className}`}>
        <div
          ref={scrollContainerRef}
          className="h-full overflow-y-auto"
        >
          {renderContent()}
        </div>
      </div>
    )
  }

  // 卡片模式渲染
  return (
    <div
      className={`absolute top-4 left-4 right-4 bg-white backdrop-blur-lg rounded-3xl border border-slate-200/50 shadow-2xl shadow-slate-200/40 transition-all duration-300 ease-in-out ${className}`}
      style={{
        height: isExpanded ? `${height}%` : '60px',
        zIndex: 60,
        minHeight: '60px',
        maxHeight: '90%'
      }}
    >
      {/* 卡片头部 */}
      <div className="px-5 py-4 flex items-center justify-between border-b border-slate-200/50">
        <div className="flex items-center space-x-3">
          <div className="p-1.5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <h3 className="font-semibold text-slate-800 text-sm">结构化笔记</h3>
          {/* 简化的状态指示 */}
          {noteStatus === 'streaming' && (
            <div className="flex items-center gap-1.5 ml-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span className="text-xs text-blue-600 font-medium">生成中</span>
              {!shouldAutoFollow && (
                <div className="flex items-center gap-1 ml-1">
                  <EyeOff className="w-3 h-3 text-amber-500" />
                  <span className="text-xs text-amber-600">已暂停</span>
                </div>
              )}
            </div>
          )}
          {noteStatus === 'analyzing' && (
            <div className="flex items-center gap-1.5 ml-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
              <span className="text-xs text-purple-600 font-medium">分析中</span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* 保存按钮 */}
          {canSave && (
            <button
              onClick={handleSaveNote}
              disabled={isSaving}
              className={`p-1.5 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 ${
                saveStatus === 'success'
                  ? 'bg-green-100 text-green-600 hover:bg-green-200'
                  : saveStatus === 'error'
                  ? 'bg-red-100 text-red-600 hover:bg-red-200'
                  : 'hover:bg-slate-100 text-slate-600 hover:text-blue-600'
              } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={
                saveStatus === 'success'
                  ? '保存成功'
                  : saveStatus === 'error'
                  ? '保存失败，点击重试'
                  : '保存到知识库'
              }
            >
              {isSaving ? (
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              ) : saveStatus === 'success' ? (
                <Check className="w-4 h-4" />
              ) : saveStatus === 'error' ? (
                <X className="w-4 h-4" />
              ) : (
                <Save className="w-4 h-4" />
              )}
            </button>
          )}

          {/* 折叠展开按钮 */}
          <button
            onClick={handleToggle}
            className="p-1.5 rounded-lg hover:bg-slate-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            title={isExpanded ? "折叠" : "展开"}
          >
            {isExpanded ? (
              <ChevronUp className="w-4 h-4 text-slate-600" />
            ) : (
              <ChevronDown className="w-4 h-4 text-slate-600" />
            )}
          </button>
        </div>
      </div>

      {/* 卡片内容 - 可滚动，支持折叠展开 */}
      {isExpanded && (
        <div
          ref={scrollContainerRef}
          className="flex-1 overflow-y-auto px-4 py-4"
          style={{ height: 'calc(100% - 80px)' }}
          onScroll={handleManualScroll}
        >
          {renderContent()}
        </div>
      )}

      {/* 拖拽手柄 - 更圆润的设计 */}
      <div
        ref={dragHandleRef}
        className="absolute bottom-0 left-0 right-0 h-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-b-3xl cursor-ns-resize hover:from-blue-500/40 hover:to-purple-500/40 transition-all duration-200"
        title="拖拽调整高度"
      >
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-1.5 bg-slate-400/80 rounded-full"></div>
      </div>
    </div>
  )
})

OptimizedStructuredNotes.displayName = 'OptimizedStructuredNotes'

export default OptimizedStructuredNotes
