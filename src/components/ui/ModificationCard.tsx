'use client'

import React from 'react'
import { Check, X, Plus, Edit, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'

import { ModificationInfo } from '@/lib/store'

interface ModificationCardProps {
  modification: ModificationInfo
  onAccept: (id: string) => void
  onReject: (id: string) => void
  onLocate: (id: string) => void
  className?: string
}

export const ModificationCard: React.FC<ModificationCardProps> = ({
  modification,
  onAccept,
  onReject,
  onLocate: _onLocate,
  className
}) => {
  const getTypeIcon = () => {
    switch (modification.type) {
      case 'add':
        return <Plus size={16} className="text-green-600" />
      case 'modify':
        return <Edit size={16} className="text-blue-600" />
      case 'delete':
        return <Trash2 size={16} className="text-red-600" />
    }
  }

  const getTypeColor = () => {
    switch (modification.type) {
      case 'add':
        return 'border-green-200 bg-green-50'
      case 'modify':
        return 'border-blue-200 bg-blue-50'
      case 'delete':
        return 'border-red-200 bg-red-50'
    }
  }

  const getTypeText = () => {
    switch (modification.type) {
      case 'add':
        return '添加'
      case 'modify':
        return '修改'
      case 'delete':
        return '删除'
    }
  }

  const isProcessed = modification.accepted || modification.rejected

  return (
    <div className={cn(
      "border rounded-lg p-3 transition-all duration-200",
      getTypeColor(),
      isProcessed && "opacity-60",
      modification.accepted && "border-green-400 bg-green-100",
      modification.rejected && "border-red-400 bg-red-100",
      className
    )}>
      {/* 头部 */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {getTypeIcon()}
          <span className="text-sm font-medium text-gray-700">
            {getTypeText()} · {modification.section}
          </span>
        </div>
        
        {!isProcessed && (
          <div className="flex items-center gap-1">
            <button
              onClick={() => onAccept(modification.id)}
              className="p-1 hover:bg-green-200 rounded text-green-600 transition-colors"
              title="接受修改"
            >
              <Check size={14} />
            </button>
            <button
              onClick={() => onReject(modification.id)}
              className="p-1 hover:bg-red-200 rounded text-red-600 transition-colors"
              title="拒绝修改"
            >
              <X size={14} />
            </button>
          </div>
        )}

        {isProcessed && (
          <div className="flex items-center">
            {modification.accepted && (
              <div className="flex items-center gap-1 text-green-600 text-xs">
                <Check size={12} />
                已接受
              </div>
            )}
            {modification.rejected && (
              <div className="flex items-center gap-1 text-red-600 text-xs">
                <X size={12} />
                已拒绝
              </div>
            )}
          </div>
        )}
      </div>

      {/* 内容 */}
      <div className="text-sm text-gray-800 mb-2">
        <div className="bg-white bg-opacity-60 rounded p-2 text-sm">
          {modification.content}
        </div>
      </div>

      {/* 原因 */}
      {modification.reason && (
        <div className="text-xs text-gray-600 italic">
          理由：{modification.reason}
        </div>
      )}
    </div>
  )
}

export default ModificationCard