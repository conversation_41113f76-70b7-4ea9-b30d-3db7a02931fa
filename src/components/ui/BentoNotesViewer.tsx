'use client'

import React, { useState, useEffect } from 'react'
import { FileText, Brain, Key, Target, Lightbulb, BookOpen, Hash, ChevronDown, ChevronUp, Edit3, Check, X } from 'lucide-react'
import { cn } from '@/lib/utils'
// import ObsidianStyleEditor from './ObsidianStyleEditor'

interface BentoNotesViewerProps {
  content: string
  loading: boolean
  title?: string
  onContentChange?: (content: string) => void
  streamingContent?: string
  isStreaming?: boolean
}

interface ParsedSection {
  type: 'summary' | 'keypoints' | 'details' | 'tags' | 'insights' | 'references' | 'custom'
  title: string
  content: string
  icon?: React.ReactNode
  color?: string
  size?: 'small' | 'medium' | 'large'
}

export const BentoNotesViewer: React.FC<BentoNotesViewerProps> = ({
  content,
  loading,
  title,
  onContentChange,
  streamingContent,
  isStreaming = false
}) => {
  const [sections, setSections] = useState<ParsedSection[]>([])
  const [isEditing, setIsEditing] = useState(false)
  const [editingContent, setEditingContent] = useState(content)
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set())

  // 解析Markdown内容为不同的section
  useEffect(() => {
    // 优先使用流式内容，然后是静态内容
    const contentToRender = streamingContent || content
    
    if (!contentToRender) {
      setSections([])
      return
    }

    const parsedSections: ParsedSection[] = []
    const lines = contentToRender.split('\n')
    let currentSection: ParsedSection | null = null
    let currentContent: string[] = []

    lines.forEach((line) => {
      // 检测一级标题
      if (line.startsWith('# ')) {
        if (currentSection) {
          currentSection.content = currentContent.join('\n').trim()
          parsedSections.push(currentSection)
        }
        
        const titleText = line.substring(2).trim()
        currentSection = detectSectionType(titleText)
        currentContent = []
      } else if (currentSection) {
        currentContent.push(line)
      }
    })

    // 处理最后一个section
    if (currentSection) {
      currentSection.content = currentContent.join('\n').trim()
      parsedSections.push(currentSection)
    }

    // 如果没有检测到section，将整个内容作为一个section
    if (parsedSections.length === 0 && contentToRender.trim()) {
      parsedSections.push({
        type: 'custom',
        title: isStreaming ? '正在生成笔记...' : '笔记内容',
        content: contentToRender,
        icon: <FileText className="w-5 h-5" />,
        color: 'blue',
        size: 'large'
      })
    }

    setSections(parsedSections)
  }, [content, streamingContent, isStreaming])

  // 根据标题内容判断section类型
  const detectSectionType = (title: string): ParsedSection => {
    const lowerTitle = title.toLowerCase()
    
    if (lowerTitle.includes('摘要') || lowerTitle.includes('概要') || lowerTitle.includes('summary')) {
      return {
        type: 'summary',
        title,
        content: '',
        icon: <Brain className="w-5 h-5" />,
        color: 'indigo',
        size: 'large'
      }
    } else if (lowerTitle.includes('要点') || lowerTitle.includes('关键') || lowerTitle.includes('key')) {
      return {
        type: 'keypoints',
        title,
        content: '',
        icon: <Key className="w-5 h-5" />,
        color: 'purple',
        size: 'medium'
      }
    } else if (lowerTitle.includes('目标') || lowerTitle.includes('目的')) {
      return {
        type: 'details',
        title,
        content: '',
        icon: <Target className="w-5 h-5" />,
        color: 'green',
        size: 'medium'
      }
    } else if (lowerTitle.includes('洞察') || lowerTitle.includes('见解') || lowerTitle.includes('insight')) {
      return {
        type: 'insights',
        title,
        content: '',
        icon: <Lightbulb className="w-5 h-5" />,
        color: 'amber',
        size: 'medium'
      }
    } else if (lowerTitle.includes('参考') || lowerTitle.includes('引用') || lowerTitle.includes('reference')) {
      return {
        type: 'references',
        title,
        content: '',
        icon: <BookOpen className="w-5 h-5" />,
        color: 'teal',
        size: 'small'
      }
    } else if (lowerTitle.includes('标签') || lowerTitle.includes('tag')) {
      return {
        type: 'tags',
        title,
        content: '',
        icon: <Hash className="w-5 h-5" />,
        color: 'pink',
        size: 'small'
      }
    } else {
      return {
        type: 'custom',
        title,
        content: '',
        icon: <FileText className="w-5 h-5" />,
        color: 'blue',
        size: 'medium'
      }
    }
  }

  // 切换section展开状态
  const toggleSection = (index: number) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedSections(newExpanded)
  }

  // 获取卡片样式
  const getCardStyle = (section: ParsedSection) => {
    const baseStyle = "group relative overflow-hidden rounded-2xl border transition-all duration-300"
    const glassStyle = "backdrop-blur-xl bg-white/70 border-white/20"
    const hoverStyle = "hover:shadow-2xl hover:-translate-y-1 hover:border-white/40"
    
    const colorStyles = {
      indigo: "hover:shadow-indigo-500/10 hover:border-indigo-300/50",
      purple: "hover:shadow-purple-500/10 hover:border-purple-300/50",
      green: "hover:shadow-green-500/10 hover:border-green-300/50",
      amber: "hover:shadow-amber-500/10 hover:border-amber-300/50",
      teal: "hover:shadow-teal-500/10 hover:border-teal-300/50",
      pink: "hover:shadow-pink-500/10 hover:border-pink-300/50",
      blue: "hover:shadow-blue-500/10 hover:border-blue-300/50"
    }

    const sizeStyles = {
      small: "col-span-1",
      medium: "col-span-1 lg:col-span-2",
      large: "col-span-1 lg:col-span-3"
    }

    return cn(
      baseStyle,
      glassStyle,
      hoverStyle,
      colorStyles[section.color as keyof typeof colorStyles] || colorStyles.blue,
      sizeStyles[section.size || 'medium']
    )
  }

  // 获取图标颜色
  const getIconColor = (color?: string) => {
    const colors = {
      indigo: "text-indigo-600",
      purple: "text-purple-600",
      green: "text-green-600",
      amber: "text-amber-600",
      teal: "text-teal-600",
      pink: "text-pink-600",
      blue: "text-blue-600"
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  // 保存编辑
  const handleSaveEdit = () => {
    onContentChange?.(editingContent)
    setIsEditing(false)
  }

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingContent(content)
    setIsEditing(false)
  }

  if (loading && !isStreaming) {
    return (
      <div className="min-h-screen p-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="col-span-1 lg:col-span-2">
                <div className="rounded-2xl backdrop-blur-xl bg-white/70 border border-white/20 p-8 animate-pulse">
                  <div className="h-6 bg-gray-200/50 rounded-lg w-1/3 mb-4"></div>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200/50 rounded-lg w-full"></div>
                    <div className="h-4 bg-gray-200/50 rounded-lg w-4/5"></div>
                    <div className="h-4 bg-gray-200/50 rounded-lg w-3/4"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!content && !streamingContent) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-6xl text-gray-300">📝</div>
          <div>
            <p className="text-lg font-medium text-gray-600">暂无结构化笔记</p>
            <p className="text-sm text-gray-400 mt-2">
              {isStreaming ? 'AI正在生成笔记，请稍候...' : 'AI正在分析内容，请稍候...'}
            </p>
          </div>
        </div>
      </div>
    )
  }

  // 编辑模式
  if (isEditing) {
    return (
      <div className="min-h-screen p-6">
        <div className="max-w-4xl mx-auto">
          <div className="rounded-2xl backdrop-blur-xl bg-white/90 border border-white/20 shadow-2xl overflow-hidden">
            {/* 编辑器头部 */}
            <div className="px-6 py-4 border-b border-gray-200/50 bg-gray-50/50 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800">编辑笔记</h3>
              <div className="flex items-center gap-2">
                <button
                  onClick={handleCancelEdit}
                  className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-4 h-4 inline mr-1" />
                  取消
                </button>
                <button
                  onClick={handleSaveEdit}
                  className="px-3 py-1.5 text-sm text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors"
                >
                  <Check className="w-4 h-4 inline mr-1" />
                  保存
                </button>
              </div>
            </div>
            
            {/* 编辑器内容 */}
            <div className="p-6">
              <textarea
                value={editingContent}
                onChange={(e) => setEditingContent(e.target.value)}
                placeholder="开始编写结构化笔记..."
                className="min-h-[500px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* 背景装饰 */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-indigo-200 to-purple-200 rounded-full blur-3xl opacity-20 animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-200 to-teal-200 rounded-full blur-3xl opacity-20 animate-float-delayed"></div>
      </div>

      <div className="max-w-7xl mx-auto">
        {/* 标题区域 */}
        {title && (
          <div className="mb-8 flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{title}</h1>
              <div className="flex items-center gap-3">
                <p className="text-gray-600">AI 智能整理的结构化笔记</p>
                {isStreaming && (
                  <div className="flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full border border-blue-200/50">
                    <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full animate-pulse"></div>
                    <span className="text-xs bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent font-medium">正在生成...</span>
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={() => setIsEditing(true)}
              className="px-4 py-2 bg-white/80 backdrop-blur-xl border border-gray-200/50 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2 text-gray-700"
              disabled={isStreaming}
            >
              <Edit3 className="w-4 h-4" />
              编辑笔记
            </button>
          </div>
        )}

        {/* Bento Grid 布局 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 auto-rows-min">
          {sections.map((section, index) => {
            const isExpanded = expandedSections.has(index)
            const isLongContent = section.content.length > 200
            const shouldShowToggle = isLongContent && section.size !== 'large'

            return (
              <div key={index} className={cn(getCardStyle(section), 'card-appear')} style={{ animationDelay: `${index * 100}ms` }}>
                {/* 卡片顶部光效 */}
                <div className="absolute inset-x-0 -top-px h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"></div>
                
                {/* 卡片内容 */}
                <div className="p-6">
                  {/* 标题栏 */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={cn("p-2 rounded-xl bg-white/50", getIconColor(section.color))}>
                        {section.icon}
                      </div>
                      <h2 className="text-lg font-semibold text-gray-800">{section.title}</h2>
                    </div>
                    {shouldShowToggle && (
                      <button
                        onClick={() => toggleSection(index)}
                        className="p-1 hover:bg-gray-100/50 rounded-lg transition-colors"
                      >
                        {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                      </button>
                    )}
                  </div>

                  {/* 内容区域 */}
                  <div className={cn(
                    "prose prose-sm max-w-none text-gray-700",
                    !isExpanded && shouldShowToggle && "line-clamp-4",
                    isStreaming && "streaming-text"
                  )}>
                    {section.type === 'tags' ? (
                      <div className="flex flex-wrap gap-2">
                        {section.content.split(',').map((tag, i) => (
                          <span
                            key={i}
                            className="px-3 py-1 bg-white/60 border border-gray-200/50 rounded-full text-sm text-gray-700 hover:bg-gray-50 transition-colors cursor-pointer"
                          >
                            {tag.trim()}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <div dangerouslySetInnerHTML={{ __html: parseMarkdown(section.content) }} />
                    )}
                  </div>
                </div>

                {/* 悬停时的边框光效 */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"></div>
              </div>
            )
          })}
        </div>
      </div>

    </div>
  )
}

// 简单的Markdown解析器
function parseMarkdown(text: string): string {
  return text
    .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.+?)\*/g, '<em>$1</em>')
    .replace(/^- (.+)$/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    .replace(/\n\n/g, '</p><p>')
    .replace(/^/, '<p>')
    .replace(/$/, '</p>')
}

export default BentoNotesViewer