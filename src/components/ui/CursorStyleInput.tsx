'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Send, MessageSquare, Zap, RotateCw, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { AIAssistantMode } from '@/lib/store'

interface CursorStyleInputProps {
  value: string
  onChange: (value: string) => void
  onSubmit: () => void
  mode: AIAssistantMode
  onModeChange: (mode: AIAssistantMode) => void
  isLoading?: boolean
  disabled?: boolean
  pendingModificationsCount?: number
  placeholder?: string
}

export const CursorStyleInput: React.FC<CursorStyleInputProps> = ({
  value,
  onChange,
  onSubmit,
  mode,
  onModeChange,
  isLoading = false,
  disabled = false,
  pendingModificationsCount = 0,
  placeholder
}) => {
  const [showModeSelector, setShowModeSelector] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 自动调整高度
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px'
    }
  }, [value])

  // 点击外部关闭模式选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowModeSelector(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (value.trim() && !isLoading) {
        onSubmit()
      }
    }
  }

  const handleModeSelect = (newMode: AIAssistantMode) => {
    onModeChange(newMode)
    setShowModeSelector(false)
    textareaRef.current?.focus()
  }

  const getModeConfig = (modeType: AIAssistantMode) => {
    switch (modeType) {
      case 'ask':
        return {
          icon: MessageSquare,
          label: 'Ask',
          description: '询问模式：基于内容回答问题',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          hoverColor: 'hover:bg-blue-100'
        }
      case 'agent':
        return {
          icon: Zap,
          label: 'Agent',
          description: '代理模式：智能修改和优化笔记',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          hoverColor: 'hover:bg-purple-100'
        }
    }
  }

  const currentModeConfig = getModeConfig(mode)
  const CurrentModeIcon = currentModeConfig.icon

  return (
    <div ref={containerRef} className="relative">
      {/* 修改计数提示 */}
      {pendingModificationsCount > 0 && (
        <div className="mb-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg text-center">
          <span className="text-sm text-blue-700">
            {pendingModificationsCount} 个修改待处理
          </span>
        </div>
      )}

      {/* 主输入卡片 */}
      <div className={cn(
        "relative bg-white border rounded-lg shadow-sm transition-all duration-200",
        isFocused ? "border-blue-500 shadow-md" : "border-gray-200",
        disabled && "opacity-50 cursor-not-allowed"
      )}>
        {/* 顶部模式选择器 */}
        <div className="flex items-center justify-between px-3 py-2 border-b border-gray-100">
          <div className="relative">
            <button
              onClick={() => setShowModeSelector(!showModeSelector)}
              disabled={disabled}
              className={cn(
                "flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors",
                currentModeConfig.bgColor,
                currentModeConfig.color,
                currentModeConfig.hoverColor,
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
              )}
            >
              <CurrentModeIcon size={16} />
              <span>{currentModeConfig.label}</span>
              <ChevronDown size={14} className={cn(
                "transition-transform duration-200",
                showModeSelector && "rotate-180"
              )} />
            </button>

            {/* 模式选择下拉菜单 */}
            {showModeSelector && (
              <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 w-56">
                {(['ask', 'agent'] as AIAssistantMode[]).map((modeType) => {
                  const config = getModeConfig(modeType)
                  const Icon = config.icon
                  const isSelected = mode === modeType

                  return (
                    <button
                      key={modeType}
                      onClick={() => handleModeSelect(modeType)}
                      className={cn(
                        "w-full flex items-start gap-3 px-4 py-3 text-left transition-colors",
                        isSelected ? "bg-gray-50" : "hover:bg-gray-50",
                        "first:rounded-t-lg last:rounded-b-lg"
                      )}
                    >
                      <Icon size={18} className={cn(config.color, "mt-0.5 flex-shrink-0")} />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900">{config.label}</span>
                          {isSelected && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                        <p className="text-xs text-gray-600 mt-0.5">{config.description}</p>
                      </div>
                    </button>
                  )
                })}
              </div>
            )}
          </div>

          <div className="text-xs text-gray-500">
            {mode === 'ask' ? '自由对话' : '智能优化'}
          </div>
        </div>

        {/* 输入区域 */}
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            disabled={disabled || isLoading}
            placeholder={placeholder || `${mode === 'ask' ? '输入您的问题...' : '描述您想要的修改...'}`}
            className={cn(
              "w-full px-4 py-3 resize-none border-0 focus:outline-none bg-transparent",
              "text-gray-900 placeholder-gray-500",
              "min-h-[44px] max-h-[200px]"
            )}
            rows={1}
          />

          {/* 发送按钮 */}
          <div className="absolute bottom-2 right-2">
            <button
              onClick={onSubmit}
              disabled={!value.trim() || isLoading || disabled}
              className={cn(
                "flex items-center justify-center w-8 h-8 rounded-md transition-all duration-200",
                value.trim() && !isLoading && !disabled
                  ? "bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                  : "bg-gray-100 text-gray-400 cursor-not-allowed"
              )}
              title={isLoading ? "发送中..." : "发送消息 (Enter)"}
            >
              {isLoading ? (
                <RotateCw size={16} className="animate-spin" />
              ) : (
                <Send size={16} />
              )}
            </button>
          </div>
        </div>

        {/* 底部提示 */}
        <div className="px-4 py-2 border-t border-gray-100 bg-gray-50/50">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Enter 发送，Shift + Enter 换行</span>
            {value.length > 0 && (
              <span>{value.length} 字符</span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CursorStyleInput