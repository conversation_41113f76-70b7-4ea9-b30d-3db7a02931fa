'use client'

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { ChevronUp, FileText, Maximize2, Minimize2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import BentoNotesViewer from './BentoNotesViewer'
import LightBrowser from './LightBrowser'
import SafeMarkdown from './SafeMarkdown'
// 简化的滚动配置
const SCROLL_CONFIG = {
  mobile: { breakpoint: 768 },
  scrollFollow: { throttleMs: 16 },
  manualOverride: { duration: 2000 },
  autoCollapse: {
    minNoteLongRatio: 1.5,
    autoCollapsePercent: 0.8,
    autoExpandTopPx: 100,
    collapsedHeightPx: 128
  }
}

// 简化的埋点函数
const trackScrollEvent = (eventName: string, data?: any) => {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', eventName, {
      event_category: 'scroll_interaction',
      ...data
    })
  }
}

interface CardFlowLayoutProps {
  activeTab: {
    id: string
    title: string
    sourceType: 'url' | 'text'
    sourceData: string
    originalContent: string
    aiNoteMarkdown: string
    aiAnalyzing?: boolean
  }
  streamingNote: string
  onContentChange?: (content: string) => void
}

export const CardFlowLayout: React.FC<CardFlowLayoutProps> = ({
  activeTab,
  streamingNote,
  onContentChange
}) => {
  // 使用简化配置
  const scrollConfig = SCROLL_CONFIG
  
  const [isNotesExpanded, setIsNotesExpanded] = useState(true)
  const [isOriginalExpanded, setIsOriginalExpanded] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const [currentSection, setCurrentSection] = useState<'notes' | 'original'>('notes')
  const [isMobile, setIsMobile] = useState(false)
  const [isUserScrolling, setIsUserScrolling] = useState(false)
  
  const containerRef = useRef<HTMLDivElement>(null)
  const notesRef = useRef<HTMLDivElement>(null)
  const originalRef = useRef<HTMLDivElement>(null)
  const lastScrollTimeRef = useRef<number>(0)
  const manualOverrideRef = useRef<number>(0)
  const animationFrameRef = useRef<number>()
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= SCROLL_CONFIG.mobile.breakpoint)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 节流的滚动进度更新
  const updateScrollProgress = useCallback(() => {
    if (!containerRef.current) return
    
    const now = Date.now()
    if (now - lastScrollTimeRef.current < SCROLL_CONFIG.scrollFollow.throttleMs) {
      return
    }
    lastScrollTimeRef.current = now
    
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current
    const maxScroll = scrollHeight - clientHeight
    const progress = maxScroll > 0 ? scrollTop / maxScroll : 0
    setScrollProgress(Math.max(0, Math.min(1, progress)))
    
    // 检测当前区域
    if (notesRef.current && originalRef.current) {
      const notesBottom = notesRef.current.offsetTop + notesRef.current.offsetHeight
      const newSection = scrollTop < notesBottom - clientHeight / 2 ? 'notes' : 'original'
      setCurrentSection(newSection)
    }
  }, [])

  // 智能折叠/展开逻辑 - 优化：流式输出时不自动折叠
  const checkAutoCollapseExpand = useCallback(() => {
    if (!containerRef.current || !notesRef.current || isMobile) return
    
    // 流式输出时不自动折叠
    if (streamingNote) return
    
    const now = Date.now()
    // 如果用户刚手动操作过，跳过自动逻辑
    if (now - manualOverrideRef.current < SCROLL_CONFIG.manualOverride.duration) {
      return
    }
    
    const { scrollTop, clientHeight } = containerRef.current
    const notesHeight = notesRef.current.offsetHeight
    const viewportHeight = clientHeight
    
    // 检查是否满足折叠条件
    const isNoteLongEnough = notesHeight > SCROLL_CONFIG.autoCollapse.minNoteLongRatio * viewportHeight
    const hasScrolledFarEnough = scrollTop > SCROLL_CONFIG.autoCollapse.autoCollapsePercent * notesHeight
    
    if (isNotesExpanded && isNoteLongEnough && hasScrolledFarEnough) {
      // 自动折叠
      setIsNotesExpanded(false)
      trackScrollEvent('NOTE_AUTO_COLLAPSE', {
        scrollTop,
        notesHeight,
        viewportHeight
      })
    } else if (!isNotesExpanded && scrollTop < SCROLL_CONFIG.autoCollapse.autoExpandTopPx) {
      // 自动展开
      setIsNotesExpanded(true)
      trackScrollEvent('NOTE_AUTO_EXPAND', {
        scrollTop,
        notesHeight,
        viewportHeight
      })
    }
  }, [isNotesExpanded, isMobile, streamingNote])

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
    }
    
    animationFrameRef.current = requestAnimationFrame(() => {
      updateScrollProgress()
      checkAutoCollapseExpand()
    })
  }, [updateScrollProgress, checkAutoCollapseExpand])

  // 检测用户滚动状态
  useEffect(() => {
    if (!containerRef.current) return
    
    const handleScrollStart = () => {
      setIsUserScrolling(true)
      clearTimeout(scrollTimeoutRef.current)
      scrollTimeoutRef.current = setTimeout(() => {
        setIsUserScrolling(false)
      }, 150)
    }
    
    const container = containerRef.current
    container.addEventListener('scroll', handleScrollStart, { passive: true })
    container.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      container.removeEventListener('scroll', handleScrollStart)
      container.removeEventListener('scroll', handleScroll)
      clearTimeout(scrollTimeoutRef.current)
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [handleScroll])

  // 手动折叠/展开
  const toggleNotesExpanded = useCallback(() => {
    manualOverrideRef.current = Date.now()
    const wasExpanded = isNotesExpanded
    setIsNotesExpanded(prev => !prev)
    
    // 埋点统计
    trackScrollEvent(wasExpanded ? 'NOTE_MANUAL_COLLAPSE' : 'NOTE_MANUAL_EXPAND')
  }, [isNotesExpanded])

  // 滚动到指定区域
  const scrollToSection = useCallback((section: 'notes' | 'original') => {
    if (!containerRef.current || !notesRef.current || !originalRef.current) return
    
    manualOverrideRef.current = Date.now()
    const targetElement = section === 'notes' ? notesRef.current : originalRef.current
    let targetTop = targetElement.offsetTop
    
    // 如果是移动端，直接切换标签页
    if (isMobile) {
      setCurrentSection(section)
      return
    }
    
    // 如果要滚动到笔记区且当前是折叠状态，先展开
    if (section === 'notes' && !isNotesExpanded) {
      setIsNotesExpanded(true)
      // 给展开动画一点时间
      setTimeout(() => {
        containerRef.current?.scrollTo({
          top: targetTop - 80,
          behavior: 'smooth'
        })
      }, 100)
    } else {
      containerRef.current.scrollTo({
        top: targetTop - 80,
        behavior: 'smooth'
      })
    }
  }, [isMobile, isNotesExpanded])

  const noteContent = streamingNote || activeTab.aiNoteMarkdown

  // 移动端标签页视图
  if (isMobile) {
    return (
      <div className="h-full flex flex-col">
        {/* 滚动进度条 */}
        <div className="h-1 bg-gray-100">
          <div 
            className="h-full bg-gradient-to-r from-indigo-500 to-purple-500 transition-all duration-150 ease-out"
            style={{ width: `${scrollProgress * 100}%` }}
          />
        </div>
        
        {/* 标签页导航 */}
        <div className="flex bg-gray-50 border-b sticky top-0 z-10">
          <button
            onClick={() => setCurrentSection('notes')}
            className={cn(
              "flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500",
              currentSection === 'notes'
                ? "border-indigo-500 text-indigo-700 bg-white"
                : "border-transparent text-gray-600 hover:text-gray-900"
            )}
            aria-current={currentSection === 'notes' ? 'page' : undefined}
          >
            <div className="flex items-center justify-center gap-2">
              <FileText className="w-4 h-4" />
              结构化笔记
              {(streamingNote || activeTab.aiAnalyzing) && (
                <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse" />
              )}
            </div>
          </button>
          <button
            onClick={() => setCurrentSection('original')}
            className={cn(
              "flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500",
              currentSection === 'original'
                ? "border-indigo-500 text-indigo-700 bg-white"
                : "border-transparent text-gray-600 hover:text-gray-900"
            )}
            aria-current={currentSection === 'original' ? 'page' : undefined}
          >
            <div className="flex items-center justify-center gap-2">
              <Maximize2 className="w-4 h-4" />
              原文内容
            </div>
          </button>
        </div>
        
        {/* 标签页内容 */}
        <div className="flex-1 overflow-hidden relative">
          <div className={cn(
            "h-full absolute inset-0 transition-transform duration-300 ease-in-out",
            currentSection === 'notes' ? 'translate-x-0' : '-translate-x-full'
          )}>
            <div className="h-full overflow-y-auto p-4">
              <BentoNotesViewer
                content={activeTab.aiNoteMarkdown}
                loading={activeTab.aiAnalyzing || false}
                title={activeTab.title}
                onContentChange={onContentChange}
                streamingContent={streamingNote}
                isStreaming={!!streamingNote || (activeTab.aiAnalyzing && !activeTab.aiNoteMarkdown)}
              />
            </div>
          </div>
          <div className={cn(
            "h-full absolute inset-0 transition-transform duration-300 ease-in-out",
            currentSection === 'original' ? 'translate-x-0' : 'translate-x-full'
          )}>
            <div className="h-full overflow-y-auto">
              {activeTab.sourceType === 'url' ? (
                <div className="p-4">
                  <div className="rounded-lg overflow-hidden shadow-lg border border-gray-200">
                    <LightBrowser
                      url={activeTab.sourceData}
                      title={activeTab.title}
                      onLoadComplete={() => {}}
                      onError={() => {}}
                      className="h-[600px]"
                    />
                  </div>
                </div>
              ) : (
                <div className="p-4">
                  <div className="w-full">
                    <h2 className="text-xl font-bold text-gray-900 mb-6 border-b border-gray-200 pb-4">{activeTab.title}</h2>
                    <div className="prose prose-base max-w-none prose-headings:text-gray-900 prose-headings:font-semibold prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-4 prose-ul:my-4 prose-ol:my-4 prose-li:my-2 prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:my-4 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100 prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto">
                      <SafeMarkdown>
                        {activeTab.originalContent || activeTab.sourceData}
                      </SafeMarkdown>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="relative h-full overflow-hidden">
      {/* 滚动进度条 */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gray-100 z-50">
        <div 
          className="h-full bg-gradient-to-r from-indigo-500 to-purple-500 transition-all duration-150"
          style={{ width: `${scrollProgress * 100}%` }}
        />
      </div>

      {/* 快速导航按钮 */}
      <div className="absolute top-4 right-4 z-40 flex gap-2">
        <button
          onClick={() => scrollToSection('notes')}
          className={cn(
            "px-3 py-1.5 rounded-lg backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg text-sm font-medium transition-all focus:outline-none focus:ring-2 focus:ring-indigo-500",
            currentSection === 'notes' ? "text-indigo-600 border-indigo-200" : "text-gray-600 hover:text-gray-900"
          )}
          aria-current={currentSection === 'notes' ? 'page' : undefined}
          onKeyDown={(e) => {
            if (e.key === 'ArrowRight') {
              scrollToSection('original')
            }
          }}
        >
          <FileText className="w-4 h-4 mr-1 inline" />
          结构化笔记
        </button>
        <button
          onClick={() => scrollToSection('original')}
          className={cn(
            "px-3 py-1.5 rounded-lg backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg text-sm font-medium transition-all focus:outline-none focus:ring-2 focus:ring-indigo-500",
            currentSection === 'original' ? "text-indigo-600 border-indigo-200" : "text-gray-600 hover:text-gray-900"
          )}
          aria-current={currentSection === 'original' ? 'page' : undefined}
          onKeyDown={(e) => {
            if (e.key === 'ArrowLeft') {
              scrollToSection('notes')
            }
          }}
        >
          <Maximize2 className="w-4 h-4 mr-1 inline" />
          原文内容
        </button>
      </div>

      {/* 主滚动容器 */}
      <div 
        ref={containerRef}
        className="h-full overflow-y-auto overflow-x-hidden smooth-scroll no-overscroll"
        style={{
          scrollbarGutter: 'stable'
        }}
      >
        {/* 结构化笔记卡片（Bento Grid风格） */}
        <div 
          ref={notesRef}
          className={cn(
            "relative transition-all duration-500 ease-out",
            isNotesExpanded ? "min-h-screen" : "h-32"
          )}
        >
          {/* 折叠状态的迷你卡片 */}
          {!isNotesExpanded && (
            <div className="sticky top-0 z-30 h-32 backdrop-blur-xl bg-white/95 border-b border-gray-200/50 shadow-lg">
              <div className="h-full px-6 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-xl bg-indigo-100/50">
                    <FileText className="w-6 h-6 text-indigo-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">结构化笔记</h3>
                    <div className="flex items-center gap-2">
                      <p className="text-sm text-gray-600">AI 整理的核心要点</p>
                      {(!!streamingNote || (activeTab.aiAnalyzing && !activeTab.aiNoteMarkdown)) && (
                        <div className="flex items-center gap-1">
                          <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full animate-pulse"></div>
                          <span className="text-xs text-indigo-600">生成中...</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <button
                  onClick={toggleNotesExpanded}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-all flex items-center gap-2 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  aria-expanded={isNotesExpanded}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      toggleNotesExpanded()
                    }
                  }}
                >
                  <Maximize2 className="w-4 h-4" />
                  展开查看
                </button>
              </div>
            </div>
          )}

          {/* 展开状态的完整笔记 */}
          {isNotesExpanded && (
            <div className="animate-in fade-in-0 slide-in-from-top-3 duration-500">
              <div className={!!streamingNote ? 'streaming-content' : ''}>
                <BentoNotesViewer
                  content={activeTab.aiNoteMarkdown}
                  loading={activeTab.aiAnalyzing || false}
                  title={activeTab.title}
                  onContentChange={onContentChange}
                  streamingContent={streamingNote}
                  isStreaming={!!streamingNote || (activeTab.aiAnalyzing && !activeTab.aiNoteMarkdown)}
                />
              </div>
              
              {/* 折叠按钮 - 智能显示 */}
              {useMemo(() => {
                if (!notesRef.current || !containerRef.current) return null
                const notesHeight = notesRef.current.offsetHeight
                const viewportHeight = containerRef.current.clientHeight
                return notesHeight > SCROLL_CONFIG.autoCollapse.minNoteLongRatio * viewportHeight
              }, [isNotesExpanded]) && (
                <div className="sticky bottom-4 z-30 flex justify-center mt-8 mb-4">
                  <button
                    onClick={toggleNotesExpanded}
                    className="px-6 py-3 backdrop-blur-xl bg-white/90 border border-gray-200/50 rounded-full shadow-lg hover:bg-gray-50 hover:shadow-xl transition-all flex items-center gap-2 text-gray-700 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    aria-expanded={isNotesExpanded}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault()
                        toggleNotesExpanded()
                      }
                    }}
                  >
                    <ChevronUp className="w-4 h-4" />
                    收起笔记
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 分隔线（仅在展开时显示） */}
        {isNotesExpanded && (
          <div className="relative h-24 flex items-center justify-center">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
            <div className="relative px-4 bg-white text-sm text-gray-500 flex items-center gap-2">
              <Minimize2 className="w-4 h-4" />
              原文内容
            </div>
          </div>
        )}

        {/* 原文内容区域 */}
        <div 
          ref={originalRef}
          className="min-h-screen pb-8"
        >
          {activeTab.sourceType === 'url' ? (
            <div className="mx-auto max-w-6xl px-6">
              <div className="rounded-2xl overflow-hidden shadow-2xl border border-gray-200/50">
                <LightBrowser
                  url={activeTab.sourceData}
                  title={activeTab.title}
                  onLoadComplete={() => {}}
                  onError={() => {}}
                  className="h-[800px]"
                />
              </div>
            </div>
          ) : (
            <div className="w-full px-6">
              <div className="max-w-none">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 border-b border-gray-200 pb-4">{activeTab.title}</h2>
                <div className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-headings:font-semibold prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-4 prose-ul:my-4 prose-ol:my-4 prose-li:my-2 prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:my-4 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100 prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto">
                  <SafeMarkdown>
                    {activeTab.originalContent || activeTab.sourceData}
                  </SafeMarkdown>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

    </div>
  )
}

export default CardFlowLayout