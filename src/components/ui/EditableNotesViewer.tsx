'use client'

import React, { useState } from 'react'
import { Download, Share2 } from 'lucide-react'
// import InlineDiffEditor, { DiffModification } from './InlineDiffEditor'
// import ObsidianStyleEditor from './ObsidianStyleEditor'
import { useAppStore, useActivePendingModifications, useActiveStreamingNote } from '@/lib/store'
import { cn } from '@/lib/utils'

interface EditableNotesViewerProps {
  content: string
  loading: boolean
  title?: string
  onContentChange?: (content: string) => void
}

export const EditableNotesViewer: React.FC<EditableNotesViewerProps> = ({
  content,
  loading,
  title,
  onContentChange
}) => {
  const [showDiff, setShowDiff] = useState(false)
  
  const { 
    acceptModification, 
    rejectModification, 
    acceptAllModifications,
    updateTab
  } = useAppStore()
  const activeTab = useAppStore(state => state.tabs.find(tab => tab.id === state.activeTabId))
  const pendingModifications = useActivePendingModifications()
  const streamingNote = useActiveStreamingNote()

  // 处理内容变更
  const handleContentChange = (newContent: string) => {
    if (activeTab) {
      updateTab(activeTab.id, { aiNoteMarkdown: newContent })
      onContentChange?.(newContent)
    }
  }

  // 将修改信息转换为内部格式
  const convertToDiffModifications = () => {
    return pendingModifications.map(mod => ({
      id: mod.id,
      type: mod.type,
      lineStart: mod.lineStart || 1,
      lineEnd: mod.lineEnd || 1,
      originalText: '', // 这里需要根据实际情况填入
      modifiedText: mod.content,
      reason: mod.reason,
      accepted: mod.accepted,
      rejected: mod.rejected
    }))
  }


  const handleAcceptModification = (modId: string) => {
    if (activeTab) {
      acceptModification(activeTab.id, modId)
    }
  }

  const handleRejectModification = (modId: string) => {
    if (activeTab) {
      rejectModification(activeTab.id, modId)
    }
  }

  const handleAcceptAllModifications = () => {
    if (activeTab) {
      acceptAllModifications(activeTab.id)
    }
  }


  if (loading) {
    return (
      <div className="h-full bg-white rounded-lg border border-border">
        <div className="flex flex-col items-center justify-center h-96 space-y-4">
          <div className="flex space-x-2">
            <div className="w-3 h-3 bg-primary rounded-full dot-flashing"></div>
            <div className="w-3 h-3 bg-primary rounded-full dot-flashing"></div>
            <div className="w-3 h-3 bg-primary rounded-full dot-flashing"></div>
          </div>
          <p className="text-muted-foreground">正在生成笔记...</p>
        </div>
      </div>
    )
  }

  if (!content) {
    return (
      <div className="h-full bg-white rounded-lg border border-border flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-6xl text-muted-foreground/30">📝</div>
          <div>
            <p className="text-lg font-medium text-muted-foreground">暂无结构化笔记</p>
            <p className="text-sm text-muted-foreground/60 mt-2">
              AI正在分析内容，请稍候...
            </p>
          </div>
        </div>
      </div>
    )
  }

  const diffModifications = convertToDiffModifications()
  const hasPendingModifications = diffModifications.some(mod => !mod.accepted && !mod.rejected)
  const isStreaming = streamingNote.length > 0 || (activeTab?.aiAnalyzing && !activeTab?.aiNoteMarkdown)

  return (
    <div className="h-full flex flex-col bg-white rounded-lg border border-border">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200/50 bg-gray-50/50">
        <div>
          {title && (
            <h1 className="text-lg font-semibold text-gray-900 leading-tight">
              {title}
            </h1>
          )}
          <div className="flex items-center gap-2">
            <p className="text-sm text-gray-600">结构化笔记</p>
            {isStreaming && (
              <div className="flex items-center gap-1 text-xs text-blue-600">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                正在生成...
              </div>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* 修改提示 */}
          {hasPendingModifications && (
            <div className="flex items-center space-x-2 mr-4">
              <span className="text-sm text-blue-600">
                {diffModifications.filter(mod => !mod.accepted && !mod.rejected).length} 个待处理修改
              </span>
              <button
                onClick={() => setShowDiff(!showDiff)}
                className={cn(
                  "px-2 py-1 text-xs rounded transition-colors",
                  showDiff ? "bg-blue-500 text-white" : "bg-blue-100 text-blue-600 hover:bg-blue-200"
                )}
              >
                {showDiff ? '隐藏' : '显示'}差异
              </button>
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            <button className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200 flex items-center">
              <Download className="w-4 h-4 mr-1.5" />
              导出
            </button>
            <button className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200 flex items-center">
              <Share2 className="w-4 h-4 mr-1.5" />
              分享
            </button>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        {showDiff && diffModifications.length > 0 ? (
          // Diff模式
          <div className="h-full overflow-y-auto p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-sm font-medium text-gray-700">待处理的修改</h3>
                <button
                  onClick={handleAcceptAllModifications}
                  className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
                >
                  接受全部
                </button>
              </div>
              {diffModifications.map((mod: any) => (
                <div key={mod.id} className="border rounded p-3 space-y-2">
                  <div className="text-sm text-gray-600">{mod.reason}</div>
                  <div className="font-mono text-sm bg-gray-100 p-2 rounded">{mod.modifiedText}</div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleAcceptModification(mod.id)}
                      className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
                    >
                      接受
                    </button>
                    <button
                      onClick={() => handleRejectModification(mod.id)}
                      className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
                    >
                      拒绝
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          // Obsidian 风格逐行编辑器
          <div className="h-full overflow-y-auto overflow-x-hidden p-6">
            <textarea
              value={streamingNote || content}
              onChange={(e) => handleContentChange(e.target.value)}
              placeholder="开始编写结构化笔记..."
              className="w-full h-full p-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm resize-none"
              readOnly={loading}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default EditableNotesViewer