'use client'

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import RecommendedQuestions from '@/components/ui/RecommendedQuestions'
import { useAppStore, useActiveTab, useActiveChatMessages, useActiveRecommendedQuestions, useCurrentModifications } from '@/lib/store'
import ModificationCard from '@/components/ui/ModificationCard'
import CursorStyleInput from '@/components/ui/CursorStyleInput'
// import { ModificationInfo } from '@/lib/store'
import { Sparkles } from 'lucide-react'
import { cn } from '@/lib/utils'

const AIAssistant: React.FC = () => {
  const { 
    addChatMessage, 
    updateChatMessage, 
    // setRecommendedQuestions,
    aiAssistantMode,
    setAIAssistantMode,
    activeMiddleTab,
    addModifications,
    acceptModification,
    rejectModification,
    acceptAllModifications
  } = useAppStore()
  const activeTab = useActiveTab()
  const chatMessages = useActiveChatMessages()
  const recommendedQuestions = useActiveRecommendedQuestions()
  const currentModifications = useCurrentModifications()
  const [chatInput, setChatInput] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  // 使用useMemo优化计算值
  const hasChatMessages = useMemo(() => chatMessages.length > 0, [chatMessages.length])

  // 优化推荐问题显示条件
  const shouldShowRecommendedQuestions = useMemo(() => {
    return recommendedQuestions.length > 0 && !hasChatMessages
  }, [recommendedQuestions.length, hasChatMessages])

  // 计算未处理的修改数量
  const pendingModificationsCount = useMemo(() => {
    return currentModifications.filter(mod => !mod.accepted && !mod.rejected).length
  }, [currentModifications])

  // 根据当前tab获取上下文
  const getCurrentContext = useCallback(() => {
    if (!activeTab) return null
    
    switch (activeMiddleTab) {
      case 'original':
        return {
          type: 'original',
          content: activeTab.originalContent || activeTab.sourceData
        }
      case 'notes':
        return {
          type: 'notes',
          content: activeTab.aiNoteMarkdown
        }
      case 'diff':
        const diffContent = useAppStore.getState().diffContent
        return {
          type: 'diff',
          content: diffContent || activeTab.aiNoteMarkdown
        }
      default:
        return null
    }
  }, [activeTab, activeMiddleTab])

  // 处理聊天提交
  const handleChatSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!chatInput.trim() || !activeTab || isStreaming) return

    const message = chatInput.trim()
    setChatInput('')
    setIsStreaming(true)

    try {
      // 添加用户消息
      addChatMessage(activeTab.id, {
        role: 'user',
        content: message
      })

      // 创建AI回复的临时消息
      const assistantMessageId = addChatMessage(activeTab.id, {
        role: 'assistant',
        content: ''
      })

      // 获取当前上下文
      const context = getCurrentContext()
      if (!context) {
        throw new Error('无法获取当前上下文，请确保已选择文档')
      }

      // 检查是否有内容
      if (!activeTab.originalContent && !activeTab.aiNoteMarkdown) {
        throw new Error('当前文档内容为空，请等待处理完成或重新上传文档')
      }

      // 准备对话历史
      const chatHistoryForApi = chatMessages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))

      // 根据模式处理请求 - 两种模式都使用流式输出
      const response = await fetch('/api/chat-with-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          context: {
            originalContent: activeTab.originalContent || '',
            aiNote: activeTab.aiNoteMarkdown || '',
          },
          chatHistory: chatHistoryForApi,
          mode: aiAssistantMode
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `请求失败 (${response.status})`)
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流，请稍后重试')
      }

      let fullResponse = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') continue

            try {
              const parsed = JSON.parse(data)
              
              if (parsed.content) {
                // 常规内容流式更新
                fullResponse += parsed.content
                updateChatMessage(activeTab.id, assistantMessageId, fullResponse)
              } else if (parsed.type === 'modifications' && aiAssistantMode === 'agent') {
                // Agent模式的修改建议
                const noteModifications = parsed.modifications
                if (noteModifications && noteModifications.hasModifications) {
                  try {
                    const modifications = noteModifications.modifications.map((mod: {
                    type: string;
                    section?: string;
                    content: string;
                    reason?: string;
                    lineStart?: number;
                    lineEnd?: number;
                  }) => ({
                      type: mod.type,
                      section: mod.section || '未指定章节',
                      content: mod.content,
                      reason: mod.reason || '根据对话内容建议',
                      lineStart: mod.lineStart || 1,
                      lineEnd: mod.lineEnd || 1
                    }))
                    
                    // 只有当有有效修改时才添加到store
                    if (modifications.length > 0) {
                      addModifications(activeTab.id, modifications)
                      
                      // diff预览功能已移除
                    }
                  } catch (error) {
                    console.error('解析修改建议失败:', error)
                  }
                }
              }
            } catch (e) {
              // 忽略解析错误
              console.debug('JSON parse error:', e)
            }
          }
        }
      }

      // 检查是否收到了完整的响应
      if (!fullResponse.trim()) {
        throw new Error('服务器返回的内容为空，请重试')
      }
    } catch (error) {
      console.error('聊天错误:', error)
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: `抱歉，处理您的请求时出现错误：${errorMessage}`
      })
    } finally {
      setIsStreaming(false)
    }
  }

  // 处理推荐问题点击
  const handleQuestionClick = (question: string) => {
    setChatInput(question)
    // 自动提交
    const form = document.getElementById('chat-form') as HTMLFormElement
    if (form) {
      form.requestSubmit()
    }
  }

  // 处理修改接受
  const handleAcceptModification = (modId: string) => {
    if (activeTab) {
      acceptModification(activeTab.id, modId)
    }
  }

  // 处理修改拒绝
  const handleRejectModification = (modId: string) => {
    if (activeTab) {
      rejectModification(activeTab.id, modId)
    }
  }

  // 处理定位修改
  const handleLocateModification = (_modId: string) => {
    // 切换到结构化笔记tab
    useAppStore.getState().setActiveMiddleTab('notes')
    // 这里可以添加滚动到特定行的逻辑
  }

  // 处理一键接受所有修改
  const handleAcceptAllModifications = () => {
    if (activeTab) {
      acceptAllModifications(activeTab.id)
    }
  }

  // 滚动到底部
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }, [chatMessages])

  // 生成推荐问题
  useEffect(() => {
    if (activeTab && activeTab.aiNoteMarkdown && recommendedQuestions.length === 0) {
      // 可以在这里调用生成推荐问题的API
    }
  }, [activeTab, recommendedQuestions])

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 顶部标题栏 */}
      <div className="p-4 border-b border-gray-200 bg-gray-50/50">
        <div className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-primary" />
          <h2 className="text-lg font-semibold text-gray-900">AI 助手</h2>
        </div>
      </div>

      {/* 聊天内容区域 */}
      <div 
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {/* 推荐问题 */}
        {shouldShowRecommendedQuestions && (
          <div className="mb-4">
            <RecommendedQuestions
              questions={recommendedQuestions}
              onQuestionClick={handleQuestionClick}
            />
          </div>
        )}

        {/* 修改卡片 */}
        {currentModifications.length > 0 && (
          <div className="mb-4 space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-700">
                本次修改建议 ({pendingModificationsCount} 个待处理)
              </h3>
              {pendingModificationsCount > 0 && (
                <button
                  onClick={handleAcceptAllModifications}
                  className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                >
                  接受全部
                </button>
              )}
            </div>
            <div className="space-y-2">
              {currentModifications.map((modification) => (
                <ModificationCard
                  key={modification.id}
                  modification={modification}
                  onAccept={handleAcceptModification}
                  onReject={handleRejectModification}
                  onLocate={handleLocateModification}
                />
              ))}
            </div>
          </div>
        )}

        {/* 聊天消息 */}
        {chatMessages.map((message) => (
          <div
            key={message.id}
            className={cn(
              "flex",
              message.role === 'user' ? "justify-end" : "justify-start"
            )}
          >
            <div
              className={cn(
                "max-w-[80%] rounded-lg px-4 py-2",
                message.role === 'user'
                  ? "bg-primary text-white"
                  : "bg-gray-100 text-gray-900"
              )}
            >
              {message.role === 'user' ? (
                <p className="text-sm">{message.content}</p>
              ) : (
                <SafeMarkdown className="text-sm prose prose-sm max-w-none">
                  {message.content}
                </SafeMarkdown>
              )}
            </div>
          </div>
        ))}

        {/* 加载状态 */}
        {isStreaming && (
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-lg px-4 py-2">
              <ModernLoader />
            </div>
          </div>
        )}
      </div>

      {/* Cursor样式输入框 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50/50">
        <CursorStyleInput
          value={chatInput}
          onChange={setChatInput}
          onSubmit={() => {
            const form = document.getElementById('chat-form') as HTMLFormElement
            if (form) {
              form.requestSubmit()
            } else {
              handleChatSubmit({ preventDefault: () => {} } as React.FormEvent)
            }
          }}
          mode={aiAssistantMode}
          onModeChange={setAIAssistantMode}
          isLoading={isStreaming}
          disabled={!activeTab}
          pendingModificationsCount={pendingModificationsCount}
        />
        
        {/* 隐藏的form，保持原有提交逻辑 */}
        <form
          id="chat-form"
          onSubmit={handleChatSubmit}
          className="hidden"
        >
          <input type="submit" />
        </form>
      </div>
    </div>
  )
}

export default AIAssistant